import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Paper,
  useTheme,
  useMediaQuery,
  Avatar,
  Rating,
  Chip,
  CircularProgress,
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import ChildCareIcon from '@mui/icons-material/ChildCare';
import FaceIcon from '@mui/icons-material/Face';
import PsychologyIcon from '@mui/icons-material/Psychology';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { useAuth } from '../contexts/AuthContext';

export default function Home() {
  const { isAuthenticated } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isSmall = useMediaQuery(theme.breakpoints.down('sm'));
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    // Simulate loading and check for errors
    const timer = setTimeout(() => {
      try {
        // Any initialization code can go here
        setLoading(false);
      } catch (err) {
        console.error("Error initializing Home page:", err);
        setError(true);
        setLoading(false);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Featured doctors data
  const featuredDoctors = [
    {
      id: 1,
      name: 'Dr. Richard James',
      specialty: 'Cardiologist',
      rating: 4.9,
      image: 'https://via.placeholder.com/300x300/f5f5f5/333333?text=Dr.+Richard+James',
      availability: true,
    },
    {
      id: 2,
      name: 'Dr. Emily Larson',
      specialty: 'Dermatologist',
      rating: 4.8,
      image: 'https://via.placeholder.com/300x300/f5f5f5/333333?text=Dr.+Emily+Larson',
      availability: true,
    },
    {
      id: 3,
      name: 'Dr. Sarah Patel',
      specialty: 'Pediatrician',
      rating: 4.7,
      image: 'https://via.placeholder.com/300x300/f5f5f5/333333?text=Dr.+Sarah+Patel',
      availability: true,
    },
    {
      id: 4,
      name: 'Dr. Christopher Lee',
      specialty: 'Neurologist',
      rating: 4.9,
      image: 'https://via.placeholder.com/300x300/f5f5f5/333333?text=Dr.+Christopher+Lee',
      availability: true,
    },
    {
      id: 5,
      name: 'Dr. Jennifer Garcia',
      specialty: 'Psychiatrist',
      rating: 4.8,
      image: 'https://via.placeholder.com/300x300/f5f5f5/333333?text=Dr.+Jennifer+Garcia',
      availability: true,
    },
    {
      id: 6,
      name: 'Dr. Andrew Williams',
      specialty: 'Orthopedist',
      rating: 4.7,
      image: 'https://via.placeholder.com/300x300/f5f5f5/333333?text=Dr.+Andrew+Williams',
      availability: true,
    },
    {
      id: 7,
      name: 'Dr. Christopher Davis',
      specialty: 'Ophthalmologist',
      rating: 4.9,
      image: 'https://via.placeholder.com/300x300/f5f5f5/333333?text=Dr.+Christopher+Davis',
      availability: true,
    },
    {
      id: 8,
      name: 'Dr. Timothy White',
      specialty: 'Cardiologist',
      rating: 4.8,
      image: 'https://via.placeholder.com/300x300/f5f5f5/333333?text=Dr.+Timothy+White',
      availability: true,
    },
  ];

  // Specialties data
  const specialties = [
    {
      id: 1,
      title: 'General doctor',
      icon: <PersonIcon sx={{ fontSize: 40, color: 'white' }} />,
      color: '#6A5ACD',
    },
    {
      id: 2,
      title: 'Dermatologist',
      icon: <FaceIcon sx={{ fontSize: 40, color: 'white' }} />,
      color: '#6A5ACD',
    },
    {
      id: 3,
      title: 'Pediatric doctor',
      icon: <ChildCareIcon sx={{ fontSize: 40, color: 'white' }} />,
      color: '#6A5ACD',
    },
    {
      id: 4,
      title: 'Neurologist',
      icon: <PsychologyIcon sx={{ fontSize: 40, color: 'white' }} />,
      color: '#6A5ACD',
    },
    {
      id: 5,
      title: 'Ophthalmologist',
      icon: <VisibilityIcon sx={{ fontSize: 40, color: 'white' }} />,
      color: '#6A5ACD',
    },
    {
      id: 6,
      title: 'Cardiologist',
      icon: <LocalHospitalIcon sx={{ fontSize: 40, color: 'white' }} />,
      color: '#6A5ACD',
    },
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="md" sx={{ py: 8, textAlign: 'center' }}>
        <Typography variant="h4" color="error" gutterBottom>
          Something went wrong
        </Typography>
        <Typography variant="body1" paragraph>
          We're having trouble loading this page. Please try refreshing.
        </Typography>
        <Button
          variant="contained"
          color="primary"
          onClick={() => window.location.reload()}
          sx={{ mt: 2 }}
        >
          Refresh Page
        </Button>
      </Container>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: theme.palette.mode === 'dark'
            ? 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)'
            : 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)',
          pt: { xs: 12, md: 16 },
          pb: { xs: 12, md: 16 },
          overflow: 'hidden',
          position: 'relative',
          color: theme.palette.text.primary,
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: theme.palette.mode === 'dark'
              ? 'radial-gradient(circle at 30% 20%, rgba(129, 140, 248, 0.3) 0%, transparent 50%), radial-gradient(circle at 70% 80%, rgba(244, 114, 182, 0.2) 0%, transparent 50%)'
              : 'radial-gradient(circle at 30% 20%, rgba(79, 70, 229, 0.1) 0%, transparent 50%), radial-gradient(circle at 70% 80%, rgba(219, 39, 119, 0.08) 0%, transparent 50%)',
            zIndex: 1,
          },
        }}
        className="fade-in"
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Box sx={{ position: 'relative', zIndex: 2 }} className="slide-up">
                <Typography
                  variant="h1"
                  component="h1"
                  sx={{
                    fontSize: { xs: '2.5rem', sm: '3rem', md: '4rem', lg: '4.5rem' },
                    fontWeight: 800,
                    lineHeight: 1.1,
                    mb: 3,
                    background: theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, #f1f5f9 0%, #94a3b8 100%)'
                      : 'linear-gradient(135deg, #0f172a 0%, #475569 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                  }}
                >
                  Book Appointments With{' '}
                  <Box
                    component="span"
                    sx={{
                      background: theme.palette.mode === 'dark'
                        ? 'linear-gradient(135deg, #818cf8 0%, #f472b6 100%)'
                        : 'linear-gradient(135deg, #4f46e5 0%, #db2777 100%)',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      backgroundClip: 'text',
                    }}
                  >
                    Trusted Doctors
                  </Box>
                </Typography>
                <Typography
                  variant="h5"
                  paragraph
                  sx={{
                    mb: 6,
                    maxWidth: '90%',
                    color: 'text.secondary',
                    fontWeight: 400,
                    lineHeight: 1.6,
                  }}
                >
                  Experience seamless healthcare with our network of verified medical professionals.
                  Schedule appointments, manage your health records, and get expert care—all in one place.
                </Typography>
                <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap', alignItems: 'center' }}>
                  <Button
                    variant="contained"
                    size="large"
                    component={Link}
                    to={isAuthenticated ? "/profile" : "/register"}
                    sx={{
                      py: 2,
                      px: 6,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      borderRadius: 4,
                      background: theme.palette.mode === 'dark'
                        ? 'linear-gradient(135deg, #818cf8 0%, #a5b4fc 100%)'
                        : 'linear-gradient(135deg, #4f46e5 0%, #6366f1 100%)',
                      boxShadow: theme.palette.mode === 'dark'
                        ? '0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -4px rgba(0, 0, 0, 0.4)'
                        : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        transform: 'translateY(-3px)',
                        boxShadow: theme.palette.mode === 'dark'
                          ? '0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 8px 10px -6px rgba(0, 0, 0, 0.5)'
                          : '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
                      }
                    }}
                  >
                    {isAuthenticated ? 'View Dashboard' : 'Get Started'}
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    component={Link}
                    to="/doctors"
                    sx={{
                      py: 2,
                      px: 6,
                      fontSize: '1.1rem',
                      fontWeight: 500,
                      borderRadius: 4,
                      borderWidth: '2px',
                      borderColor: theme.palette.mode === 'dark' ? '#475569' : '#cbd5e1',
                      color: 'text.primary',
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        borderWidth: '2px',
                        borderColor: 'primary.main',
                        backgroundColor: theme.palette.mode === 'dark'
                          ? 'rgba(129, 140, 248, 0.08)'
                          : 'rgba(79, 70, 229, 0.04)',
                        transform: 'translateY(-1px)',
                      }
                    }}
                  >
                    Browse Doctors
                  </Button>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  display: { xs: 'none', md: 'block' },
                  position: 'relative',
                  zIndex: 2,
                }}
                className="scale-in"
              >
                <Box
                  sx={{
                    position: 'relative',
                    borderRadius: 6,
                    overflow: 'hidden',
                    background: theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%)'
                      : 'linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%)',
                    backdropFilter: 'blur(20px)',
                    border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
                    boxShadow: theme.palette.mode === 'dark'
                      ? '0 25px 50px -12px rgba(0, 0, 0, 0.7)'
                      : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      transform: 'translateY(-8px) rotateY(5deg)',
                      boxShadow: theme.palette.mode === 'dark'
                        ? '0 35px 60px -12px rgba(0, 0, 0, 0.8)'
                        : '0 35px 60px -12px rgba(0, 0, 0, 0.3)',
                    },
                  }}
                >
                  <img
                    src="https://via.placeholder.com/600x500/f8fafc/4f46e5?text=Modern+Healthcare+Dashboard"
                    alt="Modern Healthcare Platform"
                    style={{
                      width: '100%',
                      height: 'auto',
                      display: 'block',
                    }}
                  />
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: theme.palette.mode === 'dark'
                        ? 'linear-gradient(135deg, rgba(129, 140, 248, 0.1) 0%, rgba(244, 114, 182, 0.05) 100%)'
                        : 'linear-gradient(135deg, rgba(79, 70, 229, 0.05) 0%, rgba(219, 39, 119, 0.03) 100%)',
                    }}
                  />
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Find by Specialty Section */}
      <Container maxWidth="lg" sx={{ py: { xs: 10, md: 16 } }}>
        <Box sx={{ textAlign: 'center', mb: 10 }} className="fade-in">
          <Typography
            variant="h2"
            component="h2"
            sx={{
              fontWeight: 700,
              mb: 3,
              background: theme.palette.mode === 'dark'
                ? 'linear-gradient(135deg, #f1f5f9 0%, #94a3b8 100%)'
                : 'linear-gradient(135deg, #0f172a 0%, #475569 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}
          >
            Find by Specialty
          </Typography>
          <Typography
            variant="h6"
            color="text.secondary"
            sx={{
              mt: 2,
              maxWidth: '600px',
              mx: 'auto',
              fontWeight: 400,
              lineHeight: 1.6,
            }}
          >
            Connect with specialized healthcare professionals across various medical fields.
            Our platform makes it easy to find the right expert for your needs.
          </Typography>
        </Box>

        <Grid container spacing={4} justifyContent="center">
          {specialties.map((specialty, index) => (
            <Grid item xs={6} sm={4} md={2} key={specialty.id}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textAlign: 'center',
                  p: 3,
                  borderRadius: 4,
                  background: theme.palette.mode === 'dark'
                    ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.6) 0%, rgba(51, 65, 85, 0.4) 100%)'
                    : 'linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%)',
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  cursor: 'pointer',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: theme.palette.mode === 'dark'
                      ? '0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 8px 10px -6px rgba(0, 0, 0, 0.5)'
                      : '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
                    '& .specialty-avatar': {
                      transform: 'scale(1.1)',
                      boxShadow: theme.palette.mode === 'dark'
                        ? '0 10px 15px -3px rgba(129, 140, 248, 0.4), 0 4px 6px -4px rgba(129, 140, 248, 0.3)'
                        : '0 10px 15px -3px rgba(79, 70, 229, 0.2), 0 4px 6px -4px rgba(79, 70, 229, 0.1)',
                    },
                  },
                }}
                className="hover-lift"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <Avatar
                  className="specialty-avatar"
                  sx={{
                    width: 80,
                    height: 80,
                    mb: 3,
                    background: theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, #818cf8 0%, #a5b4fc 100%)'
                      : 'linear-gradient(135deg, #4f46e5 0%, #6366f1 100%)',
                    boxShadow: theme.palette.mode === 'dark'
                      ? '0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.3)'
                      : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  }}
                >
                  {specialty.icon}
                </Avatar>
                <Typography
                  variant="subtitle1"
                  fontWeight="600"
                  sx={{
                    color: 'text.primary',
                    fontSize: '0.95rem',
                  }}
                >
                  {specialty.title}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* Top Doctors Section */}
      <Container maxWidth="lg" sx={{ py: { xs: 10, md: 16 } }}>
        <Box sx={{ textAlign: 'center', mb: 10 }} className="fade-in">
          <Typography
            variant="h2"
            component="h2"
            sx={{
              fontWeight: 700,
              mb: 3,
              background: theme.palette.mode === 'dark'
                ? 'linear-gradient(135deg, #f1f5f9 0%, #94a3b8 100%)'
                : 'linear-gradient(135deg, #0f172a 0%, #475569 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
            }}
          >
            Top Doctors To Book
          </Typography>
          <Typography
            variant="h6"
            color="text.secondary"
            sx={{
              mt: 2,
              maxWidth: '600px',
              mx: 'auto',
              fontWeight: 400,
              lineHeight: 1.6,
            }}
          >
            Meet our featured healthcare professionals who are highly rated by patients
            and recognized for their expertise in their respective fields.
          </Typography>
        </Box>

        <Grid container spacing={4}>
          {featuredDoctors.slice(0, 8).map((doctor, index) => (
            <Grid item xs={12} sm={6} md={3} key={doctor.id}>
              <Card
                elevation={0}
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  borderRadius: 5,
                  overflow: 'hidden',
                  background: theme.palette.mode === 'dark'
                    ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%)'
                    : 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.7) 100%)',
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateY(-12px)',
                    boxShadow: theme.palette.mode === 'dark'
                      ? '0 25px 50px -12px rgba(0, 0, 0, 0.7)'
                      : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                    '& .doctor-image': {
                      transform: 'scale(1.05)',
                    },
                  },
                }}
                className="hover-lift"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <Box sx={{ position: 'relative', overflow: 'hidden' }}>
                  <CardMedia
                    className="doctor-image"
                    component="img"
                    height="240"
                    image={doctor.image}
                    alt={doctor.name}
                    sx={{
                      transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    }}
                  />
                  {doctor.availability && (
                    <Chip
                      label="Available"
                      size="small"
                      sx={{
                        position: 'absolute',
                        top: 12,
                        left: 12,
                        fontWeight: 600,
                        fontSize: '0.75rem',
                        background: theme.palette.mode === 'dark'
                          ? 'linear-gradient(135deg, #34d399 0%, #10b981 100%)'
                          : 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                        color: 'white',
                        border: 'none',
                        boxShadow: theme.palette.mode === 'dark'
                          ? '0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.3)'
                          : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
                      }}
                    />
                  )}
                  <Box
                    sx={{
                      position: 'absolute',
                      bottom: 0,
                      left: 0,
                      right: 0,
                      height: '50%',
                      background: theme.palette.mode === 'dark'
                        ? 'linear-gradient(to top, rgba(30, 41, 59, 0.8) 0%, transparent 100%)'
                        : 'linear-gradient(to top, rgba(255, 255, 255, 0.8) 0%, transparent 100%)',
                    }}
                  />
                </Box>
                <CardContent sx={{ flexGrow: 1, p: 3 }}>
                  <Typography
                    variant="h6"
                    component="h3"
                    fontWeight="700"
                    gutterBottom
                    sx={{
                      color: 'text.primary',
                      fontSize: '1.1rem',
                    }}
                  >
                    {doctor.name}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    gutterBottom
                    sx={{
                      fontWeight: 500,
                      mb: 2,
                    }}
                  >
                    {doctor.specialty}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Rating
                      value={doctor.rating}
                      precision={0.1}
                      size="small"
                      readOnly
                      sx={{
                        '& .MuiRating-iconFilled': {
                          color: theme.palette.mode === 'dark' ? '#fbbf24' : '#f59e0b',
                        },
                      }}
                    />
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={{ fontWeight: 500 }}
                    >
                      {doctor.rating}
                    </Typography>
                  </Box>
                </CardContent>
                <CardActions sx={{ p: 3, pt: 0 }}>
                  <Button
                    variant="contained"
                    fullWidth
                    sx={{
                      py: 1.5,
                      borderRadius: 3,
                      fontWeight: 600,
                      fontSize: '0.875rem',
                      background: theme.palette.mode === 'dark'
                        ? 'linear-gradient(135deg, #818cf8 0%, #a5b4fc 100%)'
                        : 'linear-gradient(135deg, #4f46e5 0%, #6366f1 100%)',
                      transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                      '&:hover': {
                        background: theme.palette.mode === 'dark'
                          ? 'linear-gradient(135deg, #6366f1 0%, #818cf8 100%)'
                          : 'linear-gradient(135deg, #4338ca 0%, #4f46e5 100%)',
                        transform: 'translateY(-1px)',
                      },
                    }}
                  >
                    Book Appointment
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ textAlign: 'center', mt: 6 }}>
          <Button
            variant="outlined"
            size="large"
            component={Link}
            to="/doctors"
            sx={{
              py: 1.5,
              px: 4,
              borderRadius: 25,
            }}
          >
            View All
          </Button>
        </Box>
      </Container>

      {/* Book Appointment Section */}
      <Box
        sx={{
          background: theme.palette.mode === 'dark'
            ? 'linear-gradient(135deg, #483D8B 0%, #6A5ACD 100%)'
            : 'linear-gradient(135deg, #6A5ACD 0%, #9370DB 100%)',
          py: { xs: 6, md: 10 },
          color: 'white',
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={7}>
              <Typography
                variant="h3"
                component="h2"
                fontWeight="bold"
                gutterBottom
              >
                Book Appointment With 100+ Trusted Doctors
              </Typography>
              <Button
                variant="contained"
                size="large"
                component={Link}
                to="/doctors"
                sx={{
                  mt: 3,
                  py: 1.5,
                  px: 4,
                  bgcolor: 'white',
                  color: '#6A5ACD',
                  fontWeight: 500,
                  '&:hover': {
                    bgcolor: 'rgba(255, 255, 255, 0.9)',
                  }
                }}
              >
                Book Appointment
              </Button>
            </Grid>
            <Grid item xs={12} md={5}>
              <Box
                sx={{
                  display: { xs: 'none', md: 'block' },
                  position: 'relative',
                }}
              >
                <img
                  src="https://via.placeholder.com/500x400/f5f5f5/333333?text=Doctor"
                  alt="Doctor"
                  style={{
                    width: '100%',
                    height: 'auto',
                    borderRadius: '16px',
                  }}
                />
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Box>
  );
}
