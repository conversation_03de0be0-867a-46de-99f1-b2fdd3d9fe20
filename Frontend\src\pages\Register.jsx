import { useState, useEffect } from 'react';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Button,
  TextField,
  Typography,
  Container,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  FormHelperText,
  Paper,
  useTheme,
  useMediaQuery,
  InputAdornment,
  IconButton,
  Divider,
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import authService from '../services/authService';
import HomeIcon from '@mui/icons-material/Home';
import PersonIcon from '@mui/icons-material/Person';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import BadgeIcon from '@mui/icons-material/Badge';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import GoogleLoginButton from '../components/GoogleLoginButton';
import <PERSON><PERSON>aptch<PERSON> from '../components/ReCaptcha';

// Create a function to get the validation schema based on whether it's Google registration
const getValidationSchema = (isGoogleReg) => {
  // Base schema with fields required for both regular and Google registration
  const baseSchema = {
    gender: Yup.string().required('Gender is required'),
    role: Yup.string().required('Role is required'),
    captchaToken: Yup.string().required('Please complete the CAPTCHA verification'),
  };

  // Add fields required only for regular registration
  if (!isGoogleReg) {
    return Yup.object({
      ...baseSchema,
      firstName: Yup.string().required('First name is required'),
      lastName: Yup.string().required('Last name is required'),
      email: Yup.string().email('Invalid email').required('Email is required'),
      password: Yup.string()
        .min(12, 'Password must be at least 12 characters')
        .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')
        .matches(/[a-z]/, 'Password must contain at least one lowercase letter')
        .matches(/[0-9]/, 'Password must contain at least one number')
        .matches(/[^A-Za-z0-9]/, 'Password must contain at least one special character')
        .required('Password is required'),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref('password'), null], 'Passwords must match')
        .required('Confirm password is required'),
    });
  }

  // For Google registration, we only need gender and role
  return Yup.object(baseSchema);
};

export default function Register() {
  const navigate = useNavigate();
  const location = useLocation();
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isGoogleRegistration, setIsGoogleRegistration] = useState(false);
  const [googleData, setGoogleData] = useState(null);
  const [captchaError, setCaptchaError] = useState('');
  const { login } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [initialValues, setInitialValues] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    gender: '',
    role: '',
    captchaToken: '',
  });

  // Check if we have Google data from the login page
  useEffect(() => {
    if (location.state?.googleData) {
      const { firstName, lastName, email, idToken } = location.state.googleData;
      setIsGoogleRegistration(true);
      setGoogleData(location.state.googleData);

      // Update initial values with Google data
      setInitialValues({
        firstName: firstName || '',
        lastName: lastName || '',
        email: email || '',
        password: '',
        confirmPassword: '',
        gender: '',
        role: '',
        captchaToken: '',
      });
    }
  }, [location.state]);

  const formik = useFormik({
    initialValues,
    enableReinitialize: true, // This ensures form updates when initialValues change
    validationSchema: getValidationSchema(isGoogleRegistration),
    onSubmit: async (values) => {
      setLoading(true);
      setError('');
      try {
        let response;

        if (isGoogleRegistration && googleData) {
          // Register with Google
          response = await authService.googleRegister({
            idToken: googleData.idToken,
            gender: values.gender === 'Male' ? 0 : 1,
            role: values.role === 'Doctor' ? 1 : 2,
          });

          if (response.success) {
            // Google registration successful, log in the user
            login(response, response.accessToken);
            navigate('/profile');
          } else {
            setError(response.message || 'Google registration failed');
          }
        } else {
          // Regular registration
          response = await authService.register({
            firstName: values.firstName,
            lastName: values.lastName,
            email: values.email,
            password: values.password,
            gender: values.gender === 'Male' ? 0 : 1,
            role: values.role === 'Doctor' ? 1 : 2,
          });

          if (response.success) {
            // Pass both email and password to OTP verification
            navigate('/verify-otp', {
              state: {
                email: values.email,
                password: values.password,
                message: response.message
              }
            });
          } else {
            setError(response.message || 'Registration failed');
          }
        }
      } catch (err) {
        console.error('Registration error:', err);
        setError(err.response?.data?.message || 'Registration failed');
      } finally {
        setLoading(false);
      }
    },
  });

  const handleTogglePasswordVisibility = () => {
    setShowPassword((prev) => !prev);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword((prev) => !prev);
  };

  const handleCaptchaChange = (token) => {
    formik.setFieldValue('captchaToken', token);
    setCaptchaError('');
  };

  const handleCaptchaExpired = () => {
    formik.setFieldValue('captchaToken', '');
    setCaptchaError('CAPTCHA has expired, please verify again');
  };

  const handleCaptchaError = () => {
    formik.setFieldValue('captchaToken', '');
    setCaptchaError('Error loading CAPTCHA, please refresh the page');
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: theme.palette.mode === 'dark'
          ? 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)'
          : 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: theme.palette.mode === 'dark'
            ? 'radial-gradient(circle at 30% 20%, rgba(129, 140, 248, 0.2) 0%, transparent 50%), radial-gradient(circle at 70% 80%, rgba(244, 114, 182, 0.15) 0%, transparent 50%)'
            : 'radial-gradient(circle at 30% 20%, rgba(79, 70, 229, 0.08) 0%, transparent 50%), radial-gradient(circle at 70% 80%, rgba(219, 39, 119, 0.06) 0%, transparent 50%)',
          zIndex: 1,
        },
      }}
    >
      <Container component="main" maxWidth="lg" sx={{ pt: { xs: 8, md: 12 }, pb: 8, position: 'relative', zIndex: 2 }}>
        <Grid container spacing={6} justifyContent="center" alignItems="flex-start">
          {/* Main Form */}
          <Grid item xs={12} sm={10} md={8} lg={8}>
            <Paper
              elevation={0}
              sx={{
                borderRadius: 6,
                overflow: 'hidden',
                p: { xs: 4, md: 6 },
                background: theme.palette.mode === 'dark'
                  ? 'linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.6) 100%)'
                  : 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.7) 100%)',
                backdropFilter: 'blur(20px)',
                border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                boxShadow: theme.palette.mode === 'dark'
                  ? '0 25px 50px -12px rgba(0, 0, 0, 0.7)'
                  : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              }}
              className="scale-in"
            >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
              }}
            >
            {/* Mobile Logo */}
            {isMobile && (
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 6, justifyContent: 'center' }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, #818cf8 0%, #a5b4fc 100%)'
                      : 'linear-gradient(135deg, #4f46e5 0%, #6366f1 100%)',
                    color: 'white',
                    p: 2,
                    borderRadius: 3,
                    mr: 2,
                    boxShadow: theme.palette.mode === 'dark'
                      ? '0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.3)'
                      : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  <HomeIcon sx={{ fontSize: 28 }} />
                </Box>
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    fontWeight: 700,
                    background: theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, #818cf8 0%, #f472b6 100%)'
                      : 'linear-gradient(135deg, #4f46e5 0%, #db2777 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                  }}
                >
                  Prescripto
                </Typography>
              </Box>
            )}

            <Box sx={{ mb: 6, textAlign: isMobile ? 'center' : 'left' }}>
              <Typography
                component="h1"
                variant="h3"
                sx={{
                  fontWeight: 700,
                  mb: 2,
                  background: theme.palette.mode === 'dark'
                    ? 'linear-gradient(135deg, #f1f5f9 0%, #94a3b8 100%)'
                    : 'linear-gradient(135deg, #0f172a 0%, #475569 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                }}
              >
                Create Your Account
              </Typography>
              <Typography
                variant="h6"
                color="text.secondary"
                sx={{
                  fontWeight: 400,
                  lineHeight: 1.5,
                }}
              >
                Join our secure healthcare platform and start managing your health journey
              </Typography>
            </Box>

            {isGoogleRegistration && (
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  Your Google account information has been pre-filled. Please select your <strong>Gender</strong> and <strong>Role</strong> to complete registration.
                </Typography>
              </Alert>
            )}

            {error && <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>}

            <Box component="form" onSubmit={formik.handleSubmit}>
              <Grid container spacing={3}>
                {isGoogleRegistration ? (
                  <>
                    <Grid item xs={12} sm={6}>
                      <Typography
                        variant="subtitle1"
                        color="text.primary"
                        gutterBottom
                        sx={{ fontWeight: 600, mb: 1.5 }}
                      >
                        First Name (from Google)
                      </Typography>
                      <TextField
                        fullWidth
                        id="firstName"
                        name="firstName"
                        value={formik.values.firstName}
                        InputProps={{
                          readOnly: true,
                          startAdornment: (
                            <InputAdornment position="start">
                              <PersonIcon sx={{ color: 'primary.main' }} />
                            </InputAdornment>
                          ),
                        }}
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 3,
                            backgroundColor: theme.palette.mode === 'dark'
                              ? 'rgba(129, 140, 248, 0.08)'
                              : 'rgba(79, 70, 229, 0.08)',
                            border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(129, 140, 248, 0.2)' : 'rgba(79, 70, 229, 0.2)'}`,
                          },
                          '& .MuiInputBase-input': {
                            py: 2,
                            fontSize: '1rem',
                          },
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography
                        variant="subtitle1"
                        color="text.primary"
                        gutterBottom
                        sx={{ fontWeight: 600, mb: 1.5 }}
                      >
                        Last Name (from Google)
                      </Typography>
                      <TextField
                        fullWidth
                        id="lastName"
                        name="lastName"
                        value={formik.values.lastName}
                        InputProps={{
                          readOnly: true,
                          startAdornment: (
                            <InputAdornment position="start">
                              <PersonIcon sx={{ color: 'primary.main' }} />
                            </InputAdornment>
                          ),
                        }}
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 3,
                            backgroundColor: theme.palette.mode === 'dark'
                              ? 'rgba(129, 140, 248, 0.08)'
                              : 'rgba(79, 70, 229, 0.08)',
                            border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(129, 140, 248, 0.2)' : 'rgba(79, 70, 229, 0.2)'}`,
                          },
                          '& .MuiInputBase-input': {
                            py: 2,
                            fontSize: '1rem',
                          },
                        }}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Typography
                        variant="subtitle1"
                        color="text.primary"
                        gutterBottom
                        sx={{ fontWeight: 600, mb: 1.5 }}
                      >
                        Email Address (from Google)
                      </Typography>
                      <TextField
                        fullWidth
                        id="email-display"
                        value={formik.values.email}
                        InputProps={{
                          readOnly: true,
                          startAdornment: (
                            <InputAdornment position="start">
                              <EmailIcon sx={{ color: 'primary.main' }} />
                            </InputAdornment>
                          ),
                        }}
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 3,
                            backgroundColor: theme.palette.mode === 'dark'
                              ? 'rgba(129, 140, 248, 0.08)'
                              : 'rgba(79, 70, 229, 0.08)',
                            border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(129, 140, 248, 0.2)' : 'rgba(79, 70, 229, 0.2)'}`,
                          },
                          '& .MuiInputBase-input': {
                            py: 2,
                            fontSize: '1rem',
                          },
                        }}
                      />
                    </Grid>
                  </>
                ) : (
                  <>
                    <Grid item xs={12} sm={6}>
                      <Typography
                        variant="subtitle1"
                        color="text.primary"
                        gutterBottom
                        sx={{ fontWeight: 600, mb: 1.5 }}
                      >
                        First Name
                      </Typography>
                      <TextField
                        fullWidth
                        id="firstName"
                        name="firstName"
                        placeholder="Enter your first name"
                        value={formik.values.firstName}
                        onChange={formik.handleChange}
                        error={formik.touched.firstName && Boolean(formik.errors.firstName)}
                        helperText={formik.touched.firstName && formik.errors.firstName}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <PersonIcon sx={{ color: 'text.secondary' }} />
                            </InputAdornment>
                          ),
                        }}
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 3,
                            backgroundColor: theme.palette.mode === 'dark'
                              ? 'rgba(255, 255, 255, 0.03)'
                              : 'rgba(0, 0, 0, 0.02)',
                            border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                            transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                            '&:hover': {
                              backgroundColor: theme.palette.mode === 'dark'
                                ? 'rgba(255, 255, 255, 0.05)'
                                : 'rgba(0, 0, 0, 0.04)',
                              borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
                            },
                            '&.Mui-focused': {
                              backgroundColor: theme.palette.mode === 'dark'
                                ? 'rgba(255, 255, 255, 0.06)'
                                : 'rgba(0, 0, 0, 0.06)',
                              borderColor: 'primary.main',
                              boxShadow: theme.palette.mode === 'dark'
                                ? '0 0 0 3px rgba(129, 140, 248, 0.1)'
                                : '0 0 0 3px rgba(79, 70, 229, 0.1)',
                            },
                          },
                          '& .MuiInputBase-input': {
                            py: 2,
                            fontSize: '1rem',
                          },
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography
                        variant="subtitle1"
                        color="text.primary"
                        gutterBottom
                        sx={{ fontWeight: 600, mb: 1.5 }}
                      >
                        Last Name
                      </Typography>
                      <TextField
                        fullWidth
                        id="lastName"
                        name="lastName"
                        placeholder="Enter your last name"
                        value={formik.values.lastName}
                        onChange={formik.handleChange}
                        error={formik.touched.lastName && Boolean(formik.errors.lastName)}
                        helperText={formik.touched.lastName && formik.errors.lastName}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <PersonIcon sx={{ color: 'text.secondary' }} />
                            </InputAdornment>
                          ),
                        }}
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 3,
                            backgroundColor: theme.palette.mode === 'dark'
                              ? 'rgba(255, 255, 255, 0.03)'
                              : 'rgba(0, 0, 0, 0.02)',
                            border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                            transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                            '&:hover': {
                              backgroundColor: theme.palette.mode === 'dark'
                                ? 'rgba(255, 255, 255, 0.05)'
                                : 'rgba(0, 0, 0, 0.04)',
                              borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
                            },
                            '&.Mui-focused': {
                              backgroundColor: theme.palette.mode === 'dark'
                                ? 'rgba(255, 255, 255, 0.06)'
                                : 'rgba(0, 0, 0, 0.06)',
                              borderColor: 'primary.main',
                              boxShadow: theme.palette.mode === 'dark'
                                ? '0 0 0 3px rgba(129, 140, 248, 0.1)'
                                : '0 0 0 3px rgba(79, 70, 229, 0.1)',
                            },
                          },
                          '& .MuiInputBase-input': {
                            py: 2,
                            fontSize: '1rem',
                          },
                        }}
                      />
                    </Grid>
                  </>
                )}
                {!isGoogleRegistration && (
                  <Grid item xs={12}>
                    <Typography
                      variant="subtitle1"
                      color="text.primary"
                      gutterBottom
                      sx={{ fontWeight: 600, mb: 1.5 }}
                    >
                      Email Address
                    </Typography>
                    <TextField
                      fullWidth
                      id="email"
                      name="email"
                      placeholder="Enter your email address"
                      value={formik.values.email}
                      onChange={formik.handleChange}
                      error={formik.touched.email && Boolean(formik.errors.email)}
                      helperText={formik.touched.email && formik.errors.email}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <EmailIcon sx={{ color: 'text.secondary' }} />
                          </InputAdornment>
                        ),
                      }}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 3,
                          backgroundColor: theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.03)'
                            : 'rgba(0, 0, 0, 0.02)',
                          border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                          transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                          '&:hover': {
                            backgroundColor: theme.palette.mode === 'dark'
                              ? 'rgba(255, 255, 255, 0.05)'
                              : 'rgba(0, 0, 0, 0.04)',
                            borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
                          },
                          '&.Mui-focused': {
                            backgroundColor: theme.palette.mode === 'dark'
                              ? 'rgba(255, 255, 255, 0.06)'
                              : 'rgba(0, 0, 0, 0.06)',
                            borderColor: 'primary.main',
                            boxShadow: theme.palette.mode === 'dark'
                              ? '0 0 0 3px rgba(129, 140, 248, 0.1)'
                              : '0 0 0 3px rgba(79, 70, 229, 0.1)',
                          },
                        },
                        '& .MuiInputBase-input': {
                          py: 2,
                          fontSize: '1rem',
                        },
                      }}
                    />
                  </Grid>
                )}
                {!isGoogleRegistration && (
                  <>
                    <Grid item xs={12}>
                      <Typography
                        variant="subtitle1"
                        color="text.primary"
                        gutterBottom
                        sx={{ fontWeight: 600, mb: 1.5 }}
                      >
                        Password
                      </Typography>
                      <TextField
                        fullWidth
                        id="password"
                        name="password"
                        placeholder="Create a strong password"
                        type={showPassword ? 'text' : 'password'}
                        value={formik.values.password}
                        onChange={formik.handleChange}
                        error={formik.touched.password && Boolean(formik.errors.password)}
                        helperText={formik.touched.password && formik.errors.password}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <LockIcon sx={{ color: 'text.secondary' }} />
                            </InputAdornment>
                          ),
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                aria-label="toggle password visibility"
                                onClick={handleTogglePasswordVisibility}
                                edge="end"
                                sx={{
                                  color: 'text.secondary',
                                  '&:hover': {
                                    color: 'primary.main',
                                    backgroundColor: theme.palette.mode === 'dark'
                                      ? 'rgba(129, 140, 248, 0.08)'
                                      : 'rgba(79, 70, 229, 0.04)',
                                  },
                                }}
                              >
                                {showPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 3,
                            backgroundColor: theme.palette.mode === 'dark'
                              ? 'rgba(255, 255, 255, 0.03)'
                              : 'rgba(0, 0, 0, 0.02)',
                            border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                            transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                            '&:hover': {
                              backgroundColor: theme.palette.mode === 'dark'
                                ? 'rgba(255, 255, 255, 0.05)'
                                : 'rgba(0, 0, 0, 0.04)',
                              borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
                            },
                            '&.Mui-focused': {
                              backgroundColor: theme.palette.mode === 'dark'
                                ? 'rgba(255, 255, 255, 0.06)'
                                : 'rgba(0, 0, 0, 0.06)',
                              borderColor: 'primary.main',
                              boxShadow: theme.palette.mode === 'dark'
                                ? '0 0 0 3px rgba(129, 140, 248, 0.1)'
                                : '0 0 0 3px rgba(79, 70, 229, 0.1)',
                            },
                          },
                          '& .MuiInputBase-input': {
                            py: 2,
                            fontSize: '1rem',
                          },
                        }}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <Typography
                        variant="subtitle1"
                        color="text.primary"
                        gutterBottom
                        sx={{ fontWeight: 600, mb: 1.5 }}
                      >
                        Confirm Password
                      </Typography>
                      <TextField
                        fullWidth
                        id="confirmPassword"
                        name="confirmPassword"
                        placeholder="Confirm your password"
                        type={showConfirmPassword ? 'text' : 'password'}
                        value={formik.values.confirmPassword}
                        onChange={formik.handleChange}
                        error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
                        helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <LockIcon sx={{ color: 'text.secondary' }} />
                            </InputAdornment>
                          ),
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                aria-label="toggle confirm password visibility"
                                onClick={handleToggleConfirmPasswordVisibility}
                                edge="end"
                                sx={{
                                  color: 'text.secondary',
                                  '&:hover': {
                                    color: 'primary.main',
                                    backgroundColor: theme.palette.mode === 'dark'
                                      ? 'rgba(129, 140, 248, 0.08)'
                                      : 'rgba(79, 70, 229, 0.04)',
                                  },
                                }}
                              >
                                {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            </InputAdornment>
                          ),
                        }}
                        variant="outlined"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 3,
                            backgroundColor: theme.palette.mode === 'dark'
                              ? 'rgba(255, 255, 255, 0.03)'
                              : 'rgba(0, 0, 0, 0.02)',
                            border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                            transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                            '&:hover': {
                              backgroundColor: theme.palette.mode === 'dark'
                                ? 'rgba(255, 255, 255, 0.05)'
                                : 'rgba(0, 0, 0, 0.04)',
                              borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
                            },
                            '&.Mui-focused': {
                              backgroundColor: theme.palette.mode === 'dark'
                                ? 'rgba(255, 255, 255, 0.06)'
                                : 'rgba(0, 0, 0, 0.06)',
                              borderColor: 'primary.main',
                              boxShadow: theme.palette.mode === 'dark'
                                ? '0 0 0 3px rgba(129, 140, 248, 0.1)'
                                : '0 0 0 3px rgba(79, 70, 229, 0.1)',
                            },
                          },
                          '& .MuiInputBase-input': {
                            py: 2,
                            fontSize: '1rem',
                          },
                        }}
                      />
                    </Grid>
                  </>
                )}
                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="subtitle1"
                    color="text.primary"
                    gutterBottom
                    sx={{ fontWeight: 600, mb: 1.5 }}
                  >
                    Gender
                  </Typography>
                  <FormControl
                    fullWidth
                    error={formik.touched.gender && Boolean(formik.errors.gender)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 3,
                        backgroundColor: theme.palette.mode === 'dark'
                          ? 'rgba(255, 255, 255, 0.03)'
                          : 'rgba(0, 0, 0, 0.02)',
                        border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                        transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                        '&:hover': {
                          backgroundColor: theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.05)'
                            : 'rgba(0, 0, 0, 0.04)',
                          borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
                        },
                        '&.Mui-focused': {
                          backgroundColor: theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.06)'
                            : 'rgba(0, 0, 0, 0.06)',
                          borderColor: 'primary.main',
                          boxShadow: theme.palette.mode === 'dark'
                            ? '0 0 0 3px rgba(129, 140, 248, 0.1)'
                            : '0 0 0 3px rgba(79, 70, 229, 0.1)',
                        },
                      },
                      '& .MuiSelect-select': {
                        py: 2,
                        fontSize: '1rem',
                      },
                    }}
                  >
                    <InputLabel id="gender-label">Select your gender</InputLabel>
                    <Select
                      labelId="gender-label"
                      id="gender"
                      name="gender"
                      value={formik.values.gender}
                      label="Select your gender"
                      onChange={formik.handleChange}
                      startAdornment={
                        <InputAdornment position="start">
                          <BadgeIcon sx={{ color: 'text.secondary' }} />
                        </InputAdornment>
                      }
                    >
                      <MenuItem value="Male">Male</MenuItem>
                      <MenuItem value="Female">Female</MenuItem>
                    </Select>
                    {formik.touched.gender && formik.errors.gender && (
                      <FormHelperText>{formik.errors.gender}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography
                    variant="subtitle1"
                    color="text.primary"
                    gutterBottom
                    sx={{ fontWeight: 600, mb: 1.5 }}
                  >
                    Role
                  </Typography>
                  <FormControl
                    fullWidth
                    error={formik.touched.role && Boolean(formik.errors.role)}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 3,
                        backgroundColor: theme.palette.mode === 'dark'
                          ? 'rgba(255, 255, 255, 0.03)'
                          : 'rgba(0, 0, 0, 0.02)',
                        border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
                        transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                        '&:hover': {
                          backgroundColor: theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.05)'
                            : 'rgba(0, 0, 0, 0.04)',
                          borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
                        },
                        '&.Mui-focused': {
                          backgroundColor: theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.06)'
                            : 'rgba(0, 0, 0, 0.06)',
                          borderColor: 'primary.main',
                          boxShadow: theme.palette.mode === 'dark'
                            ? '0 0 0 3px rgba(129, 140, 248, 0.1)'
                            : '0 0 0 3px rgba(79, 70, 229, 0.1)',
                        },
                      },
                      '& .MuiSelect-select': {
                        py: 2,
                        fontSize: '1rem',
                      },
                    }}
                  >
                    <InputLabel id="role-label">Select your role</InputLabel>
                    <Select
                      labelId="role-label"
                      id="role"
                      name="role"
                      value={formik.values.role}
                      label="Select your role"
                      onChange={formik.handleChange}
                      startAdornment={
                        <InputAdornment position="start">
                          <HomeIcon sx={{ color: 'text.secondary' }} />
                        </InputAdornment>
                      }
                    >
                      <MenuItem value="Doctor">Doctor</MenuItem>
                      <MenuItem value="Patient">Patient</MenuItem>
                    </Select>
                    {formik.touched.role && formik.errors.role && (
                      <FormHelperText>{formik.errors.role}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              </Grid>

              {/* CAPTCHA Component */}
              <Box sx={{ mt: 3 }}>
                <ReCaptcha
                  onChange={handleCaptchaChange}
                  onExpired={handleCaptchaExpired}
                  onError={handleCaptchaError}
                />
                {captchaError && (
                  <Alert severity="error" sx={{ mt: 1, mb: 1 }}>
                    {captchaError}
                  </Alert>
                )}
                {formik.touched.captchaToken && formik.errors.captchaToken && (
                  <Alert severity="error" sx={{ mt: 1, mb: 1 }}>
                    {formik.errors.captchaToken}
                  </Alert>
                )}
              </Box>

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                sx={{
                  mt: 6,
                  mb: 4,
                  py: 2,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  borderRadius: 4,
                  background: theme.palette.mode === 'dark'
                    ? 'linear-gradient(135deg, #818cf8 0%, #a5b4fc 100%)'
                    : 'linear-gradient(135deg, #4f46e5 0%, #6366f1 100%)',
                  boxShadow: theme.palette.mode === 'dark'
                    ? '0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -4px rgba(0, 0, 0, 0.4)'
                    : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    background: theme.palette.mode === 'dark'
                      ? 'linear-gradient(135deg, #6366f1 0%, #818cf8 100%)'
                      : 'linear-gradient(135deg, #4338ca 0%, #4f46e5 100%)',
                    transform: 'translateY(-2px)',
                    boxShadow: theme.palette.mode === 'dark'
                      ? '0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 8px 10px -6px rgba(0, 0, 0, 0.5)'
                      : '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
                  },
                  '&:disabled': {
                    background: theme.palette.mode === 'dark'
                      ? 'rgba(129, 140, 248, 0.3)'
                      : 'rgba(79, 70, 229, 0.3)',
                    color: 'rgba(255, 255, 255, 0.7)',
                  },
                }}
                disabled={loading}
              >
                {loading ? (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <CircularProgress size={20} color="inherit" />
                    <Typography variant="button">
                      {isGoogleRegistration ? 'Completing Registration...' : 'Creating Account...'}
                    </Typography>
                  </Box>
                ) : (
                  isGoogleRegistration ? 'Complete Google Registration' : 'Create Account'
                )}
              </Button>

              {!isGoogleRegistration && (
                <>
                  <Box sx={{ mt: 4, mb: 4, display: 'flex', alignItems: 'center' }}>
                    <Divider
                      sx={{
                        flexGrow: 1,
                        borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)',
                      }}
                    />
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{
                        mx: 3,
                        fontWeight: 500,
                        fontSize: '0.875rem',
                      }}
                    >
                      OR
                    </Typography>
                    <Divider
                      sx={{
                        flexGrow: 1,
                        borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)',
                      }}
                    />
                  </Box>

                  <GoogleLoginButton
                    variant="register"
                    onError={(errorMessage) => setError(errorMessage)}
                  />
                </>
              )}

              <Box sx={{ mt: 6, textAlign: 'center' }}>
                <Typography
                  variant="body1"
                  color="text.secondary"
                  sx={{ fontWeight: 400 }}
                >
                  Already have an account?{' '}
                  <Link
                    to="/login"
                    style={{
                      textDecoration: 'none',
                      color: theme.palette.primary.main,
                      fontWeight: 600,
                      transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.textDecoration = 'underline';
                      e.target.style.textUnderlineOffset = '4px';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.textDecoration = 'none';
                    }}
                  >
                    Sign in here
                  </Link>
                </Typography>
              </Box>
            </Box>
            </Box>
          </Paper>
        </Grid>

        </Grid>
      </Container>
    </Box>
  );
}