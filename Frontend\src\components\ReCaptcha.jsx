import { useRef, useEffect } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'react-google-recaptcha';
import { Box } from '@mui/material';

// Replace this with your actual reCAPTCHA site key
const RECAPTCHA_SITE_KEY = '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI'; // This is Google's test key

const ReCaptcha = ({ onChange, onExpired, onError }) => {
  const recaptchaRef = useRef(null);

  useEffect(() => {
    // Reset the captcha when the component mounts
    return () => {
      if (recaptchaRef.current) {
        recaptchaRef.current.reset();
      }
    };
  }, []);

  const handleChange = (token) => {
    if (onChange) {
      onChange(token);
    }
  };

  const handleExpired = () => {
    if (onExpired) {
      onExpired();
    }
  };

  const handleError = (err) => {
    console.error('reCAPTCHA Error:', err);
    if (onError) {
      onError(err);
    }
  };

  return (
    <Box sx={{ my: 2, display: 'flex', justifyContent: 'center' }}>
      <ReCAPTCHA
        ref={recaptchaRef}
        sitekey={RECAPTCHA_SITE_KEY}
        onChange={handleChange}
        onExpired={handleExpired}
        onError={handleError}
      />
    </Box>
  );
};

export default ReCaptcha;
