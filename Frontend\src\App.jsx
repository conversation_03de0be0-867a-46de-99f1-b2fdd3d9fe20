import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider as MuiThemeProvider, createTheme, Box, CircularProgress, CssBaseline } from '@mui/material';
import { Suspense, useMemo } from 'react';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider, useTheme as useCustomTheme } from './contexts/ThemeContext';
import GoogleAuthProvider from './components/GoogleAuthProvider';
import Register from './pages/Register';
import Login from './pages/Login';
import VerifyOTP from './pages/VerifyOTP';
import TwoFactorVerify from './pages/TwoFactorVerify';
import VerifyGoogleTwoFactor from './pages/VerifyGoogleTwoFactor';
import RecoveryCode from './pages/RecoveryCode';
import Profile from './pages/Profile';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import Home from './pages/Home';
import Doctors from './pages/Doctors';
import Services from './pages/Services';
import About from './pages/About';
import Contact from './pages/Contact';
import ProtectedRoute from './components/ProtectedRoute';
import Navbar from './components/Navbar';
import Footer from './components/Footer';

const LoadingFallback = () => (
  <Box
    display="flex"
    justifyContent="center"
    alignItems="center"
    minHeight="100vh"
  >
    <CircularProgress />
  </Box>
);

// App content with theme applied
function AppContent() {
  const { darkMode } = useCustomTheme();

  // Create theme based on dark mode state
  const theme = useMemo(() =>
    createTheme({
      palette: {
        mode: darkMode ? 'dark' : 'light',
        primary: {
          50: '#f0f4ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          300: '#a5b4fc',
          400: '#818cf8',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
          800: '#3730a3',
          900: '#312e81',
          main: darkMode ? '#818cf8' : '#4f46e5',
          light: darkMode ? '#a5b4fc' : '#6366f1',
          dark: darkMode ? '#4338ca' : '#3730a3',
          contrastText: '#ffffff',
        },
        secondary: {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#ec4899',
          600: '#db2777',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
          main: darkMode ? '#f472b6' : '#db2777',
          light: darkMode ? '#f9a8d4' : '#ec4899',
          dark: darkMode ? '#be185d' : '#9d174d',
          contrastText: '#ffffff',
        },
        error: {
          main: darkMode ? '#f87171' : '#ef4444',
          light: darkMode ? '#fca5a5' : '#f87171',
          dark: darkMode ? '#dc2626' : '#dc2626',
        },
        warning: {
          main: darkMode ? '#fbbf24' : '#f59e0b',
          light: darkMode ? '#fcd34d' : '#fbbf24',
          dark: darkMode ? '#d97706' : '#d97706',
        },
        info: {
          main: darkMode ? '#60a5fa' : '#3b82f6',
          light: darkMode ? '#93c5fd' : '#60a5fa',
          dark: darkMode ? '#2563eb' : '#1d4ed8',
        },
        success: {
          main: darkMode ? '#34d399' : '#10b981',
          light: darkMode ? '#6ee7b7' : '#34d399',
          dark: darkMode ? '#059669' : '#047857',
        },
        background: {
          default: darkMode ? '#0f172a' : '#f8fafc',
          paper: darkMode ? '#1e293b' : '#ffffff',
        },
        text: {
          primary: darkMode ? '#f1f5f9' : '#0f172a',
          secondary: darkMode ? '#94a3b8' : '#64748b',
          disabled: darkMode ? '#475569' : '#cbd5e1',
        },
        divider: darkMode ? '#334155' : '#e2e8f0',
        grey: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
      },
      typography: {
        fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif',
        h1: {
          fontWeight: 800,
          fontSize: '3.5rem',
          lineHeight: 1.1,
          letterSpacing: '-0.04em',
          '@media (max-width:600px)': {
            fontSize: '2.5rem',
          },
        },
        h2: {
          fontWeight: 700,
          fontSize: '2.75rem',
          lineHeight: 1.2,
          letterSpacing: '-0.03em',
          '@media (max-width:600px)': {
            fontSize: '2rem',
          },
        },
        h3: {
          fontWeight: 700,
          fontSize: '2.25rem',
          lineHeight: 1.2,
          letterSpacing: '-0.02em',
          '@media (max-width:600px)': {
            fontSize: '1.75rem',
          },
        },
        h4: {
          fontWeight: 600,
          fontSize: '1.875rem',
          lineHeight: 1.3,
          letterSpacing: '-0.01em',
          '@media (max-width:600px)': {
            fontSize: '1.5rem',
          },
        },
        h5: {
          fontWeight: 600,
          fontSize: '1.5rem',
          lineHeight: 1.3,
          '@media (max-width:600px)': {
            fontSize: '1.25rem',
          },
        },
        h6: {
          fontWeight: 600,
          fontSize: '1.25rem',
          lineHeight: 1.4,
          '@media (max-width:600px)': {
            fontSize: '1.125rem',
          },
        },
        subtitle1: {
          fontWeight: 500,
          fontSize: '1.125rem',
          lineHeight: 1.5,
        },
        subtitle2: {
          fontWeight: 500,
          fontSize: '1rem',
          lineHeight: 1.5,
        },
        body1: {
          fontWeight: 400,
          fontSize: '1rem',
          lineHeight: 1.6,
        },
        body2: {
          fontWeight: 400,
          fontSize: '0.875rem',
          lineHeight: 1.5,
        },
        caption: {
          fontWeight: 400,
          fontSize: '0.75rem',
          lineHeight: 1.4,
        },
        button: {
          fontWeight: 500,
          fontSize: '0.875rem',
          lineHeight: 1.5,
          textTransform: 'none',
          letterSpacing: '0.01em',
        },
      },
      shape: {
        borderRadius: 16,
      },
      spacing: 8,
      shadows: [
        'none',
        darkMode
          ? '0 1px 2px 0 rgba(0, 0, 0, 0.3)'
          : '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        darkMode
          ? '0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.3)'
          : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
        darkMode
          ? '0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -4px rgba(0, 0, 0, 0.4)'
          : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
        darkMode
          ? '0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 8px 10px -6px rgba(0, 0, 0, 0.5)'
          : '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
        darkMode
          ? '0 25px 50px -12px rgba(0, 0, 0, 0.7)'
          : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        ...Array(19).fill(darkMode
          ? '0 25px 50px -12px rgba(0, 0, 0, 0.7)'
          : '0 25px 50px -12px rgba(0, 0, 0, 0.25)')
      ],
      components: {
        MuiButton: {
          defaultProps: {
            disableElevation: true,
          },
          styleOverrides: {
            root: {
              borderRadius: 12,
              padding: '12px 24px',
              fontSize: '0.875rem',
              fontWeight: 500,
              textTransform: 'none',
              transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
              '&:hover': {
                transform: 'translateY(-1px)',
              },
            },
            contained: {
              boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
              '&:hover': {
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
              },
            },
            containedPrimary: {
              background: `linear-gradient(135deg, ${darkMode ? '#818cf8' : '#4f46e5'} 0%, ${darkMode ? '#a5b4fc' : '#6366f1'} 100%)`,
              '&:hover': {
                background: `linear-gradient(135deg, ${darkMode ? '#6366f1' : '#4338ca'} 0%, ${darkMode ? '#818cf8' : '#4f46e5'} 100%)`,
              },
            },
            outlined: {
              borderWidth: '1.5px',
              '&:hover': {
                borderWidth: '1.5px',
                backgroundColor: darkMode ? 'rgba(129, 140, 248, 0.08)' : 'rgba(79, 70, 229, 0.04)',
              },
            },
            text: {
              '&:hover': {
                backgroundColor: darkMode ? 'rgba(129, 140, 248, 0.08)' : 'rgba(79, 70, 229, 0.04)',
              },
            },
          },
        },
        MuiTextField: {
          styleOverrides: {
            root: {
              '& .MuiOutlinedInput-root': {
                borderRadius: 12,
                backgroundColor: darkMode ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)',
                transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  backgroundColor: darkMode ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.04)',
                },
                '&.Mui-focused': {
                  backgroundColor: darkMode ? 'rgba(255, 255, 255, 0.06)' : 'rgba(0, 0, 0, 0.06)',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderWidth: '2px',
                    borderColor: darkMode ? '#818cf8' : '#4f46e5',
                  },
                },
              },
            },
          },
        },
        MuiCard: {
          styleOverrides: {
            root: {
              borderRadius: 20,
              border: `1px solid ${darkMode ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
              boxShadow: darkMode
                ? '0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.3)'
                : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
              transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: darkMode
                  ? '0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -4px rgba(0, 0, 0, 0.4)'
                  : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
              },
            },
          },
        },
        MuiPaper: {
          styleOverrides: {
            root: {
              borderRadius: 20,
              border: `1px solid ${darkMode ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
              backgroundImage: 'none',
            },
            elevation1: {
              boxShadow: darkMode
                ? '0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2)'
                : '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            },
            elevation2: {
              boxShadow: darkMode
                ? '0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.3)'
                : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
            },
            elevation3: {
              boxShadow: darkMode
                ? '0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -4px rgba(0, 0, 0, 0.4)'
                : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
            },
          },
        },
        MuiAppBar: {
          styleOverrides: {
            root: {
              boxShadow: 'none',
              borderBottom: `1px solid ${darkMode ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)'}`,
              backgroundColor: darkMode ? 'rgba(15, 23, 42, 0.8)' : 'rgba(248, 250, 252, 0.8)',
              backdropFilter: 'blur(12px)',
            },
          },
        },
        MuiChip: {
          styleOverrides: {
            root: {
              borderRadius: 8,
              fontWeight: 500,
            },
          },
        },
        MuiAvatar: {
          styleOverrides: {
            root: {
              boxShadow: darkMode
                ? '0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.3)'
                : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
            },
          },
        },
        MuiAlert: {
          styleOverrides: {
            root: {
              borderRadius: 12,
              border: `1px solid`,
            },
          },
        },
      },
    }), [darkMode]);

  return (
    <MuiThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <Router>
          <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
            <Navbar />
            <Box component="main" sx={{ flexGrow: 1 }}>
              <Suspense fallback={<LoadingFallback />}>
                <Routes>
                  <Route path="/register" element={<Register />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/verify-otp" element={<VerifyOTP />} />
                  <Route path="/two-factor-verify" element={<TwoFactorVerify />} />
                  <Route path="/verify-google-2fa" element={<VerifyGoogleTwoFactor />} />
                  <Route path="/recovery-code" element={<RecoveryCode />} />
                  <Route path="/forgot-password" element={<ForgotPassword />} />
                  <Route
                    path="/reset-password"
                    element={
                      <ProtectedRoute>
                        <ResetPassword />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/profile"
                    element={
                      <ProtectedRoute>
                        <Profile />
                      </ProtectedRoute>
                    }
                  />
                  <Route path="/doctors" element={<Doctors />} />
                  <Route path="/services" element={<Services />} />
                  <Route path="/about" element={<About />} />
                  <Route path="/contact" element={<Contact />} />
                  <Route path="/" element={<Home />} />
                </Routes>
              </Suspense>
            </Box>
            <Footer />
          </Box>
        </Router>
      </AuthProvider>
    </MuiThemeProvider>
  );
}

function App() {
  return (
    <ThemeProvider>
      <GoogleAuthProvider>
        <AppContent />
      </GoogleAuthProvider>
    </ThemeProvider>
  );
}

export default App;
